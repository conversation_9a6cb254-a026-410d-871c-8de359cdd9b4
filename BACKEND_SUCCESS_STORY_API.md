# Success Story Microservice API Endpoints

## Required Backend Controller

You need to create a `SuccessStoryController.java` file in your backend with the following endpoints:

```java
package com.example.SuccessStory_Microservice.Controller;

import com.example.SuccessStory_Microservice.Service.SuccessStoryService;
import com.example.SuccessStory_Microservice.Entity.SuccessStory;
import com.example.SuccessStory_Microservice.Dto.SuccessStoryDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/success-stories")
@CrossOrigin(origins = "http://localhost:5174") // Allow frontend access
public class SuccessStoryController {

    @Autowired
    private SuccessStoryService successStoryService;

    // GET all success stories
    @GetMapping
    public ResponseEntity<List<SuccessStoryDTO>> getAllSuccessStories() {
        try {
            List<SuccessStoryDTO> stories = successStoryService.getAllSuccessStories();
            return ResponseEntity.ok(stories);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    // POST new success story
    @PostMapping
    public ResponseEntity<SuccessStory> createSuccessStory(@RequestBody SuccessStoryDTO successStoryDTO) {
        try {
            SuccessStory savedStory = successStoryService.saveSuccessStory(successStoryDTO);
            return ResponseEntity.ok(savedStory);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
}
```

## Required DTO Class

Create `SuccessStoryDTO.java`:

```java
package com.example.SuccessStory_Microservice.Dto;

public class SuccessStoryDTO {
    private String inventionId;
    private String inventor_name;
    private String investorId;
    private String message;
    private String profilePhoto;

    // Constructors
    public SuccessStoryDTO() {}

    public SuccessStoryDTO(String inventionId, String inventor_name, String investorId, String message, String profilePhoto) {
        this.inventionId = inventionId;
        this.inventor_name = inventor_name;
        this.investorId = investorId;
        this.message = message;
        this.profilePhoto = profilePhoto;
    }

    // Getters and Setters
    public String getInventionId() { return inventionId; }
    public void setInventionId(String inventionId) { this.inventionId = inventionId; }

    public String getInventor_name() { return inventor_name; }
    public void setInventor_name(String inventor_name) { this.inventor_name = inventor_name; }

    public String getInvestorId() { return investorId; }
    public void setInvestorId(String investorId) { this.investorId = investorId; }

    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }

    public String getProfilePhoto() { return profilePhoto; }
    public void setProfilePhoto(String profilePhoto) { this.profilePhoto = profilePhoto; }
}
```

## Updates Needed to Existing Files

### 1. Update SuccessStoryService.java

Your service constructor needs to match the entity constructor:

```java
public SuccessStory saveSuccessStory(SuccessStoryDTO successStoryDTO) {
    SuccessStory story = new SuccessStory(
            successStoryDTO.getInventionId(),
            successStoryDTO.getInvestorId(),
            successStoryDTO.getMessage(),
            successStoryDTO.getProfilePhoto(),
            successStoryDTO.getInventor_name()  // Add this parameter
    );
    return repository.save(story);
}
```

### 2. Update SuccessStory Entity

Fix the constructor parameter order and add missing setter:

```java
public SuccessStory(String inventionId, String investorId, String message, String profilePhoto, String inventor_name) {
    this.inventionId = inventionId;
    this.inventor_name = inventor_name;
    this.investorId = investorId;
    this.message = message;
    this.profilePhoto = profilePhoto;
}

// Add missing setter
public void setInventorName(String inventor_name) { 
    this.inventor_name = inventor_name; 
}

public void setProfilePhoto(String profilePhoto) { 
    this.profilePhoto = profilePhoto; 
}
```

## Application Properties

Make sure your `application.properties` includes:

```properties
server.port=5004
spring.data.mongodb.uri=mongodb://localhost:27017/innovest_success_stories
```

## Frontend Integration

The frontend will make requests to:
- `GET http://localhost:5004/api/success-stories` - Fetch all stories
- `POST http://localhost:5004/api/success-stories` - Add new story

Make sure your backend is running on port 5004 for the frontend to connect properly.
