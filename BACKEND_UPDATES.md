# Backend Updates Required

## PaymentController.java Updates

You need to update the success and cancel URLs in your PaymentController.java file:

### Current URLs (lines 47-48):
```java
.setSuccessUrl("http://localhost:5003/api/payments/success?session_id={CHECKOUT_SESSION_ID}")
.setCancelUrl("http://localhost:5003/api/payments/cancel")
```

### Updated URLs should be:
```java
.setSuccessUrl("http://localhost:3000/payment-success?session_id={CHECKOUT_SESSION_ID}")
.setCancelUrl("http://localhost:3000/payment-failure?session_id={CHECKOUT_SESSION_ID}")
```

## Why this change is needed:
- The current URLs redirect to your backend API endpoints
- The new URLs redirect to your React frontend pages
- This provides a better user experience with proper success/failure pages
- Users will see styled pages instead of raw JSON responses

## Additional Notes:
- Make sure your React development server is running on port 3000
- If you're using a different port, update the URLs accordingly
- The session_id parameter will be automatically passed to the frontend pages
- The frontend pages will handle displaying the appropriate success/failure messages
