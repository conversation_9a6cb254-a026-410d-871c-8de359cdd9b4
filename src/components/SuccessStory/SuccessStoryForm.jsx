import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '../ui/card';
import { Button, Input, Label } from './ui';
import { toast } from 'sonner';

const SuccessStoryForm = ({ onSubmitSuccess, onCancel }) => {
    const [isLoading, setIsLoading] = useState(false);
    const [formData, setFormData] = useState({
        inventionId: '',
        inventor_name: '',
        investorId: '',
        message: '',
        profilePhoto: ''
    });

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!formData.inventionId || !formData.inventor_name || !formData.message) {
            toast.error('Please fill in all required fields');
            return;
        }

        setIsLoading(true);

        try {
            const response = await fetch('http://localhost:5004/api/success-stories', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData),
            });

            if (response.ok) {
                toast.success('Success story added successfully!');
                setFormData({
                    inventionId: '',
                    inventor_name: '',
                    investorId: '',
                    message: '',
                    profilePhoto: ''
                });
                onSubmitSuccess();
            } else {
                toast.error('Failed to add success story');
            }
        } catch (error) {
            console.error('Error submitting success story:', error);
            toast.error('Failed to submit success story');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Card className="max-w-2xl mx-auto shadow-xl">
            <CardHeader>
                <CardTitle className="text-2xl text-center">Share Your Success Story</CardTitle>
                <p className="text-gray-600 text-center">
                    Inspire other inventors by sharing your journey with InnoVest
                </p>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-4">
                        <div>
                            <Label htmlFor="inventionId">Invention ID *</Label>
                            <Input
                                id="inventionId"
                                name="inventionId"
                                type="text"
                                placeholder="Enter your invention ID"
                                value={formData.inventionId}
                                onChange={handleInputChange}
                                className="mt-1"
                                required
                            />
                        </div>
                        <div>
                            <Label htmlFor="inventor_name">Your Name *</Label>
                            <Input
                                id="inventor_name"
                                name="inventor_name"
                                type="text"
                                placeholder="Enter your name"
                                value={formData.inventor_name}
                                onChange={handleInputChange}
                                className="mt-1"
                                required
                            />
                        </div>
                    </div>
                    
                    <div className="grid md:grid-cols-2 gap-4">
                        <div>
                            <Label htmlFor="investorId">Investor ID (Optional)</Label>
                            <Input
                                id="investorId"
                                name="investorId"
                                type="text"
                                placeholder="Enter investor ID if applicable"
                                value={formData.investorId}
                                onChange={handleInputChange}
                                className="mt-1"
                            />
                        </div>
                        <div>
                            <Label htmlFor="profilePhoto">Profile Photo URL (Optional)</Label>
                            <Input
                                id="profilePhoto"
                                name="profilePhoto"
                                type="url"
                                placeholder="Enter photo URL"
                                value={formData.profilePhoto}
                                onChange={handleInputChange}
                                className="mt-1"
                            />
                        </div>
                    </div>

                    <div>
                        <Label htmlFor="message">Your Success Story *</Label>
                        <textarea
                            id="message"
                            name="message"
                            rows="4"
                            placeholder="Share your experience with InnoVest. How did we help you achieve success?"
                            value={formData.message}
                            onChange={handleInputChange}
                            className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            required
                        />
                    </div>

                    <div className="flex gap-4 justify-end">
                        <Button
                            type="button"
                            onClick={onCancel}
                            variant="outline"
                            className="px-6"
                        >
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            disabled={isLoading}
                            className="bg-green-600 hover:bg-green-700 px-6"
                        >
                            {isLoading ? 'Submitting...' : 'Submit Story'}
                        </Button>
                    </div>
                </form>
            </CardContent>
        </Card>
    );
};

export default SuccessStoryForm;
