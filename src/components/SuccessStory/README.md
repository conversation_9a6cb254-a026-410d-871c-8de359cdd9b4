# SuccessStory Components

This folder contains modular components for handling success stories functionality in the InnoVest application.

## Components Overview

### 1. SuccessStoryCard.jsx
**Purpose**: Individual card component for displaying a single success story
**Props**:
- `story` (object): Success story data containing:
  - `inventor_name`: Name of the inventor
  - `inventionId`: ID of the invention
  - `message`: Success story message
  - `profilePhoto`: URL to profile photo (optional)
  - `investorId`: ID of investor (optional)

**Features**:
- Profile photo with fallback to user icon
- Quoted message display
- Inventor and invention information
- Hover effects and responsive design

### 2. SuccessStoryForm.jsx
**Purpose**: Form component for submitting new success stories
**Props**:
- `onSubmitSuccess`: Callback function called after successful submission
- `onCancel`: Callback function called when form is cancelled

**Features**:
- Form validation for required fields
- Loading states during submission
- Error handling with toast notifications
- Responsive grid layout
- Textarea for story message

### 3. SuccessStoryList.jsx
**Purpose**: Horizontal scrolling container for success story cards
**Props**:
- `stories`: Array of success story objects
- `refreshTrigger`: Trigger for refreshing the list

**Features**:
- Horizontal scrolling with navigation buttons
- Hidden scrollbars for clean appearance
- Empty state message
- Responsive card layout

### 4. SuccessStorySection.jsx
**Purpose**: Main container component that orchestrates all success story functionality
**Props**: None (self-contained)

**Features**:
- Fetches success stories from API
- Manages form visibility state
- Handles form submission and list refresh
- Combines all sub-components

### 5. index.js
**Purpose**: Barrel export file for easy importing

## Usage

### Basic Usage
```jsx
import { SuccessStorySection } from '../components/SuccessStory';

function AboutPage() {
  return (
    <div>
      <SuccessStorySection />
    </div>
  );
}
```

### Individual Component Usage
```jsx
import { SuccessStoryCard, SuccessStoryForm } from '../components/SuccessStory';

function CustomPage() {
  const story = {
    inventor_name: "John Doe",
    inventionId: "INV001",
    message: "Great experience with InnoVest!",
    profilePhoto: "https://example.com/photo.jpg",
    investorId: "INV123"
  };

  return (
    <div>
      <SuccessStoryCard story={story} />
      <SuccessStoryForm 
        onSubmitSuccess={() => console.log('Success!')}
        onCancel={() => console.log('Cancelled')}
      />
    </div>
  );
}
```

## API Integration

The components expect the following API endpoints:

### GET /api/success-stories
Returns array of success story objects:
```json
[
  {
    "inventor_name": "John Doe",
    "inventionId": "INV001",
    "message": "Success story message",
    "profilePhoto": "https://example.com/photo.jpg",
    "investorId": "INV123"
  }
]
```

### POST /api/success-stories
Accepts success story object and returns created story:
```json
{
  "inventor_name": "John Doe",
  "inventionId": "INV001",
  "message": "Success story message",
  "profilePhoto": "https://example.com/photo.jpg",
  "investorId": "INV123"
}
```

## Styling

Components use Tailwind CSS classes and are designed to be:
- Responsive across all screen sizes
- Consistent with the overall application design
- Accessible with proper ARIA labels and semantic HTML

## Dependencies

- React (hooks: useState, useEffect)
- Lucide React (icons)
- Sonner (toast notifications)
- Custom UI components (Card, Button, Input, Label)

## File Structure
```
src/components/SuccessStory/
├── SuccessStoryCard.jsx      # Individual story card
├── SuccessStoryForm.jsx      # Form for adding stories
├── SuccessStoryList.jsx      # Horizontal scrolling list
├── SuccessStorySection.jsx   # Main container component
├── index.js                  # Barrel exports
└── README.md                 # This file
```
