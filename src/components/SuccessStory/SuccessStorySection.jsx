import React, { useState, useEffect } from 'react';
import { Button } from './ui';
import { Plus } from 'lucide-react';
import SuccessStoryList from './SuccessStoryList';
import SuccessStoryForm from './SuccessStoryForm';

const SuccessStorySection = () => {
    const [successStories, setSuccessStories] = useState([]);
    const [showForm, setShowForm] = useState(false);
    const [refreshTrigger, setRefreshTrigger] = useState(0);

    // Fetch success stories on component mount and when refreshTrigger changes
    useEffect(() => {
        fetchSuccessStories();
    }, [refreshTrigger]);

    const fetchSuccessStories = async () => {
        try {
            const response = await fetch('http://localhost:5004/api/success-stories');
            if (response.ok) {
                const data = await response.json();
                setSuccessStories(data);
            } else {
                console.error('Failed to fetch success stories');
            }
        } catch (error) {
            console.error('Error fetching success stories:', error);
        }
    };

    const handleFormSubmitSuccess = () => {
        setShowForm(false);
        setRefreshTrigger(prev => prev + 1); // Trigger refresh
    };

    const handleFormCancel = () => {
        setShowForm(false);
    };

    return (
        <div className="mb-16">
            {/* Section Header */}
            <div className="flex justify-between items-center mb-8">
                <h2 className="text-3xl font-bold text-gray-900">Success Stories</h2>
                <Button
                    onClick={() => setShowForm(!showForm)}
                    className="bg-green-600 hover:bg-green-700 flex items-center gap-2"
                >
                    <Plus className="h-4 w-4" />
                    Add Your Story
                </Button>
            </div>

            {/* Success Stories List */}
            <SuccessStoryList stories={successStories} refreshTrigger={refreshTrigger} />

            {/* Add Success Story Form */}
            {showForm && (
                <div className="mt-16">
                    <SuccessStoryForm 
                        onSubmitSuccess={handleFormSubmitSuccess}
                        onCancel={handleFormCancel}
                    />
                </div>
            )}
        </div>
    );
};

export default SuccessStorySection;
