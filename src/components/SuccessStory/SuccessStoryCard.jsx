import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card';
import { User, Quote } from 'lucide-react';

const SuccessStoryCard = ({ story }) => {
    return (
        <Card className="min-w-[300px] max-w-[300px] shadow-lg hover:shadow-xl transition-shadow">
            <CardHeader className="text-center">
                <div className="w-16 h-16 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                    {story.profilePhoto ? (
                        <img 
                            src={story.profilePhoto} 
                            alt={story.inventor_name}
                            className="w-full h-full object-cover"
                        />
                    ) : (
                        <div className="w-full h-full flex items-center justify-center">
                            <User className="h-8 w-8 text-gray-400" />
                        </div>
                    )}
                </div>
                <CardTitle className="text-lg">{story.inventor_name}</CardTitle>
                <p className="text-sm text-gray-500">Invention ID: {story.inventionId}</p>
            </CardHeader>
            <CardContent>
                <div className="flex justify-center mb-3">
                    <Quote className="h-6 w-6 text-blue-500" />
                </div>
                <p className="text-gray-700 text-center italic">"{story.message}"</p>
                {story.investorId && (
                    <p className="text-xs text-gray-500 text-center mt-3">
                        Investor: {story.investorId}
                    </p>
                )}
            </CardContent>
        </Card>
    );
};

export default SuccessStoryCard;
