.signup-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  /* Changed to simple white background */
  background-color: #ffffff;
  padding: 20px;
}

.signup-card {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600px;
  /* Add a subtle border for better definition against white background */
  border: 1px solid #e1e8ed;
}

.header-section {
  position: relative;
  margin-bottom: 30px;
  /* Add padding top to prevent overlap */
  padding-top: 50px;
}

.back-button {
  position: absolute;
  top: 0;
  left: 0;
  background: transparent;
  border: 1px solid #667eea;
  color: #667eea;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9em;
  transition: all 0.3s ease;
  /* Ensure button doesn't overlap */
  z-index: 10;
}

.back-button:hover {
  background: #667eea;
  color: white;
}

.signup-card h1 {
  color: #333;
  margin-bottom: 8px;
  font-size: 2.5em;
  text-align: center;
  /* Remove margin-top since we added padding to header-section */
  margin-top: 0;
}

.signup-card p {
  color: #666;
  text-align: center;
  margin-bottom: 0;
  font-size: 1.1em;
}

.message {
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
  text-align: center;
  font-weight: 500;
}

.message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.warning {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.signup-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 5px;
  color: #333;
  font-weight: 500;
  font-size: 0.95em;
}

/* Style for required field asterisks */
.required-asterisk {
  color: #e74c3c;
  margin-left: 3px;
  font-weight: bold;
}

.form-group input,
.form-group select {
  padding: 12px;
  border: 2px solid #e1e1e1;
  border-radius: 6px;
  font-size: 1em;
  transition: border-color 0.3s ease;
  background-color: #ffffff;
  /* FIXED: Add explicit text color for visibility */
  color: #333333;
  /* FIXED: Ensure dropdown has light background */
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

/* FIXED: Style for date input specifically */
.form-group input[type="date"] {
  background-color: #ffffff;
  color: #333333;
  /* Make the calendar icon visible */
  color-scheme: light;
}

/* FIXED: Style for select dropdown */
.form-group select {
  background-color: #ffffff;
  color: #333333;
  /* Add custom dropdown arrow */
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  padding-right: 40px;
}

/* FIXED: Style for select options */
.form-group select option {
  background-color: #ffffff;
  color: #333333;
  padding: 8px;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Add visual indicator for required fields */
.form-group.required input,
.form-group.required select {
  border-left: 3px solid #e74c3c;
}

.submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px;
  border: none;
  border-radius: 6px;
  font-size: 1.1em;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-top: 10px;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.login-link {
  text-align: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e1e1e1;
}

.login-link p {
  margin: 0;
  color: #666;
}

.login-link a {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.login-link a:hover {
  text-decoration: underline;
}

@media (max-width: 768px) {
  .signup-container {
    padding: 10px;
    /* Keep white background on mobile */
    background-color: #ffffff;
  }
  
  .signup-card {
    padding: 30px 20px;
  }
  
  .signup-card h1 {
    font-size: 2em;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .header-section {
    /* Reduce padding on mobile */
    padding-top: 40px;
  }
  
  .back-button {
    /* Keep absolute positioning on mobile but ensure no overlap */
    position: absolute;
    top: 0;
    left: 0;
  }
}