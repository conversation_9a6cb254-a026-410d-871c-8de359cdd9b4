import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/payment/button';
import { Input } from '../components/payment/input';
import { Label } from '../components/payment/label';
import { Plus, Star, Quote, User, ChevronLeft, ChevronRight } from 'lucide-react';
import { toast } from 'sonner';

const About = () => {
    const [successStories, setSuccessStories] = useState([]);
    const [showForm, setShowForm] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [formData, setFormData] = useState({
        inventionId: '',
        inventor_name: '',
        investorId: '',
        message: '',
        profilePhoto: ''
    });

    // Fetch success stories on component mount
    useEffect(() => {
        fetchSuccessStories();
    }, []);

    const fetchSuccessStories = async () => {
        try {
            const response = await fetch('http://localhost:5004/api/success-stories');
            if (response.ok) {
                const data = await response.json();
                setSuccessStories(data);
            } else {
                console.error('Failed to fetch success stories');
            }
        } catch (error) {
            console.error('Error fetching success stories:', error);
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!formData.inventionId || !formData.inventor_name || !formData.message) {
            toast.error('Please fill in all required fields');
            return;
        }

        setIsLoading(true);

        try {
            const response = await fetch('http://localhost:5004/api/success-stories', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData),
            });

            if (response.ok) {
                toast.success('Success story added successfully!');
                setFormData({
                    inventionId: '',
                    inventor_name: '',
                    investorId: '',
                    message: '',
                    profilePhoto: ''
                });
                setShowForm(false);
                fetchSuccessStories(); // Refresh the list
            } else {
                toast.error('Failed to add success story');
            }
        } catch (error) {
            console.error('Error submitting success story:', error);
            toast.error('Failed to submit success story');
        } finally {
            setIsLoading(false);
        }
    };

    const scrollContainer = (direction) => {
        const container = document.getElementById('stories-container');
        const scrollAmount = 320; // Width of one card plus gap

        if (direction === 'left') {
            container.scrollLeft -= scrollAmount;
        } else {
            container.scrollLeft += scrollAmount;
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4">
            <div className="max-w-7xl mx-auto">
                {/* Header Section */}
                <div className="text-center mb-16">
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">About InnoVest</h1>
                    <p className="text-xl text-gray-600 max-w-4xl mx-auto mb-8">
                        InnoVest is dedicated to helping inventors protect and commercialize their innovations.
                        We provide comprehensive patent services and connect inventors with investors to bring
                        groundbreaking ideas to life.
                    </p>
                    <div className="grid md:grid-cols-3 gap-8 mt-12">
                        <div className="text-center">
                            <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                <Star className="h-8 w-8 text-blue-600" />
                            </div>
                            <h3 className="text-xl font-semibold mb-2">Expert Guidance</h3>
                            <p className="text-gray-600">Professional patent experts guide you through every step</p>
                        </div>
                        <div className="text-center">
                            <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                <User className="h-8 w-8 text-green-600" />
                            </div>
                            <h3 className="text-xl font-semibold mb-2">Investor Network</h3>
                            <p className="text-gray-600">Connect with investors ready to fund your innovation</p>
                        </div>
                        <div className="text-center">
                            <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                <Quote className="h-8 w-8 text-purple-600" />
                            </div>
                            <h3 className="text-xl font-semibold mb-2">Success Stories</h3>
                            <p className="text-gray-600">Join hundreds of successful inventors and entrepreneurs</p>
                        </div>
                    </div>
                </div>

                {/* Success Stories Section */}
                <div className="mb-16">
                    <div className="flex justify-between items-center mb-8">
                        <h2 className="text-3xl font-bold text-gray-900">Success Stories</h2>
                        <Button
                            onClick={() => setShowForm(!showForm)}
                            className="bg-green-600 hover:bg-green-700 flex items-center gap-2"
                        >
                            <Plus className="h-4 w-4" />
                            Add Your Story
                        </Button>
                    </div>

                    {/* Horizontal Scrolling Stories */}
                    <div className="relative">
                        <Button
                            onClick={() => scrollContainer('left')}
                            className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white hover:bg-gray-100 text-gray-600 shadow-lg rounded-full w-10 h-10 p-0"
                        >
                            <ChevronLeft className="h-5 w-5" />
                        </Button>

                        <div
                            id="stories-container"
                            className="flex gap-6 overflow-x-auto scrollbar-hide scroll-smooth pb-4"
                            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
                        >
                            {successStories.length > 0 ? (
                                successStories.map((story, index) => (
                                    <Card key={index} className="min-w-[300px] max-w-[300px] shadow-lg hover:shadow-xl transition-shadow">
                                        <CardHeader className="text-center">
                                            <div className="w-16 h-16 rounded-full bg-gray-200 mx-auto mb-4 overflow-hidden">
                                                {story.profilePhoto ? (
                                                    <img
                                                        src={story.profilePhoto}
                                                        alt={story.inventor_name}
                                                        className="w-full h-full object-cover"
                                                    />
                                                ) : (
                                                    <div className="w-full h-full flex items-center justify-center">
                                                        <User className="h-8 w-8 text-gray-400" />
                                                    </div>
                                                )}
                                            </div>
                                            <CardTitle className="text-lg">{story.inventor_name}</CardTitle>
                                            <p className="text-sm text-gray-500">Invention ID: {story.inventionId}</p>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="flex justify-center mb-3">
                                                <Quote className="h-6 w-6 text-blue-500" />
                                            </div>
                                            <p className="text-gray-700 text-center italic">"{story.message}"</p>
                                            {story.investorId && (
                                                <p className="text-xs text-gray-500 text-center mt-3">
                                                    Investor: {story.investorId}
                                                </p>
                                            )}
                                        </CardContent>
                                    </Card>
                                ))
                            ) : (
                                <div className="w-full text-center py-12">
                                    <p className="text-gray-500 text-lg">No success stories yet. Be the first to share yours!</p>
                                </div>
                            )}
                {/* Add Success Story Form */}
                {showForm && (
                    <div className="mb-16">
                        <Card className="max-w-2xl mx-auto shadow-xl">
                            <CardHeader>
                                <CardTitle className="text-2xl text-center">Share Your Success Story</CardTitle>
                                <p className="text-gray-600 text-center">
                                    Inspire other inventors by sharing your journey with InnoVest
                                </p>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmit} className="space-y-6">
                                    <div className="grid md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="inventionId">Invention ID *</Label>
                                            <Input
                                                id="inventionId"
                                                name="inventionId"
                                                type="text"
                                                placeholder="Enter your invention ID"
                                                value={formData.inventionId}
                                                onChange={handleInputChange}
                                                className="mt-1"
                                                required
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="inventor_name">Your Name *</Label>
                                            <Input
                                                id="inventor_name"
                                                name="inventor_name"
                                                type="text"
                                                placeholder="Enter your name"
                                                value={formData.inventor_name}
                                                onChange={handleInputChange}
                                                className="mt-1"
                                                required
                                            />
                                        </div>
                                    </div>

                                    <div className="grid md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="investorId">Investor ID (Optional)</Label>
                                            <Input
                                                id="investorId"
                                                name="investorId"
                                                type="text"
                                                placeholder="Enter investor ID if applicable"
                                                value={formData.investorId}
                                                onChange={handleInputChange}
                                                className="mt-1"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="profilePhoto">Profile Photo URL (Optional)</Label>
                                            <Input
                                                id="profilePhoto"
                                                name="profilePhoto"
                                                type="url"
                                                placeholder="Enter photo URL"
                                                value={formData.profilePhoto}
                                                onChange={handleInputChange}
                                                className="mt-1"
                                            />
                                        </div>
                                    </div>

                                    <div>
                                        <Label htmlFor="message">Your Success Story *</Label>
                                        <textarea
                                            id="message"
                                            name="message"
                                            rows="4"
                                            placeholder="Share your experience with InnoVest. How did we help you achieve success?"
                                            value={formData.message}
                                            onChange={handleInputChange}
                                            className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            required
                                        />
                                    </div>

                                    <div className="flex gap-4 justify-end">
                                        <Button
                                            type="button"
                                            onClick={() => setShowForm(false)}
                                            variant="outline"
                                            className="px-6"
                                        >
                                            Cancel
                                        </Button>
                                        <Button
                                            type="submit"
                                            disabled={isLoading}
                                            className="bg-green-600 hover:bg-green-700 px-6"
                                        >
                                            {isLoading ? 'Submitting...' : 'Submit Story'}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Company Stats */}
                <div className="bg-white rounded-lg shadow-lg p-8 mb-16">
                    <h2 className="text-3xl font-bold text-center text-gray-900 mb-8">Our Impact</h2>
                    <div className="grid md:grid-cols-4 gap-8 text-center">
                        <div>
                            <div className="text-4xl font-bold text-blue-600 mb-2">500+</div>
                            <div className="text-gray-600">Patents Filed</div>
                        </div>
                        <div>
                            <div className="text-4xl font-bold text-green-600 mb-2">$50M+</div>
                            <div className="text-gray-600">Funding Raised</div>
                        </div>
                        <div>
                            <div className="text-4xl font-bold text-purple-600 mb-2">200+</div>
                            <div className="text-gray-600">Success Stories</div>
                        </div>
                        <div>
                            <div className="text-4xl font-bold text-orange-600 mb-2">95%</div>
                            <div className="text-gray-600">Success Rate</div>
                        </div>
                    </div>
                </div>

                {/* Contact Information */}
                <div className="text-center">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">Get Started Today</h2>
                    <p className="text-xl text-gray-600 mb-8">
                        Ready to protect your innovation and connect with investors?
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button
                            onClick={() => window.location.href = '/payment'}
                            className="bg-blue-600 hover:bg-blue-700 px-8 py-3 text-lg"
                        >
                            Start Your Patent Journey
                        </Button>
                        <Button
                            variant="outline"
                            className="px-8 py-3 text-lg"
                            onClick={() => window.location.href = 'mailto:<EMAIL>'}
                        >
                            Contact Us
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default About;
