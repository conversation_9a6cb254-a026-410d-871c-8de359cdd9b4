import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '../components/ui/card';
import { Button } from '../components/payment/button';
import { Input } from '../components/payment/input';
import { Label } from '../components/payment/label';
import { Check, Star, Crown, Building } from 'lucide-react';
import { toast } from 'sonner';

const Payment = () => {
    const [selectedPlan, setSelectedPlan] = useState('');
    const [inventionId, setInventionId] = useState('');
    const [inventorEmail, setInventorEmail] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const pricingPlans = [
        {
            name: 'Standard',
            price: 100,
            icon: <Star className="h-6 w-6 text-blue-600" />,
            features: [
                'Basic patent search',
                'Standard documentation',
                'Email support',
                'Basic filing assistance',
                '30-day review period'
            ]
        },
        {
            name: 'Enterprise',
            price: 250,
            icon: <Building className="h-6 w-6 text-green-600" />,
            features: [
                'Comprehensive patent search',
                'Professional documentation',
                'Priority email support',
                'Advanced filing assistance',
                'Patent landscape analysis',
                '60-day review period',
                'One revision included'
            ],
            popular: true
        },
        {
            name: 'Premium',
            price: 500,
            icon: <Crown className="h-6 w-6 text-purple-600" />,
            features: [
                'Full patent search & analysis',
                'Expert document preparation',
                'Expedited filing process',
                '24/7 dedicated support',
                'Complete landscape analysis',
                'International filing options',
                '90-day review period',
                'Legal consultation included'
            ]
        }
    ];

    const handlePayment = async () => {
        if (!selectedPlan || !inventionId || !inventorEmail) {
            toast.error('Please fill in all required fields and select a plan');
            return;
        }

        setIsLoading(true);

        try {
            const response = await fetch(`http://localhost:5003/api/payments/${inventionId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    Payment_Package: selectedPlan,
                    Inventor_Email: inventorEmail,
                }),
            });

            const data = await response.json();

            if (response.ok && data.session_url) {
                // Redirect to Stripe Checkout
                window.location.href = data.session_url;
                toast.success('Redirecting to payment...');
            } else {
                toast.error(data.error || 'Payment initialization failed');
            }
        } catch (error) {
            console.error('Payment error:', error);
            toast.error('Failed to initialize payment. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4">
            <div className="max-w-6xl mx-auto">
                {/* Header */}
                <div className="text-center mb-12">
                    <h1 className="text-4xl font-bold text-gray-900 mb-4">Choose Your Patent Package</h1>
                    <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                        Select the perfect package for your invention. Our expert team will guide you through
                        the entire patent application process with professional support every step of the way.
                    </p>
                </div>

                {/* User Information Form */}
                <div className="max-w-md mx-auto mb-12">
                    <Card className="shadow-lg">
                        <CardHeader>
                            <CardTitle className="text-center">Your Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <Label htmlFor="inventionId">Invention ID</Label>
                                <Input
                                    id="inventionId"
                                    type="text"
                                    placeholder="Enter your invention ID"
                                    value={inventionId}
                                    onChange={(e) => setInventionId(e.target.value)}
                                    className="mt-1"
                                />
                            </div>
                            <div>
                                <Label htmlFor="email">Email Address</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    placeholder="Enter your email"
                                    value={inventorEmail}
                                    onChange={(e) => setInventorEmail(e.target.value)}
                                    className="mt-1"
                                />
                            </div>
                        </CardContent>
                    </Card>
                </div>