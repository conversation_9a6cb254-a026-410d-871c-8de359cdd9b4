// import React from 'react'
// import '../index.css'
//
//
// const Payment = () => {
//     return (
//         <div className="absolute w-full h-full">
//             <div>Payment</div>
//         </div>
//     )
// }
//
// export default Payment



import React, { useState } from 'react';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/payment/button';
import { Input } from '@/components/payment/input';
import { Label } from '@/components/payment/label';
import { Check, Star, Crown, Building } from 'lucide-react';
import { toast } from 'sonner';

interface PricingPlan {
    name: string;
    price: number;
    icon: React.ReactNode;
    features: string[];
    popular?: boolean;
}

const Payment = () => {
    const [selectedPlan, setSelectedPlan] = useState<string>('');
    const [inventionId, setInventionId] = useState<string>('');
    const [inventorEmail, setInventorEmail] = useState<string>('');
    const [isLoading, setIsLoading] = useState<boolean>(false);

    const pricingPlans: PricingPlan[] = [
        {
            name: 'Standard',
            price: 100,
            icon: <Star className="h-6 w-6 text-blue-600" />,
            features: [
                'Basic patent search',
                'Document preparation',
                'Standard filing process',
                'Email support',
                '30-day review period'
            ]
        },
        {
            name: 'Enterprise',
            price: 250,
            icon: <Building className="h-6 w-6 text-green-600" />,
            features: [
                'Comprehensive patent search',
                'Professional document prep',
                'Priority filing process',
                'Phone & email support',
                'Patent landscape analysis',
                '60-day review period'
            ],
            popular: true
        },
        {
            name: 'Premium',
            price: 500,
            icon: <Crown className="h-6 w-6 text-purple-600" />,
            features: [
                'Full patent search & analysis',
                'Expert document preparation',
                'Expedited filing process',
                '24/7 dedicated support',
                'Complete landscape analysis',
                'International filing options',
                '90-day review period',
                'Legal consultation included'
            ]
        }
    ];

    const handlePayment = async () => {
        if (!selectedPlan || !inventionId || !inventorEmail) {
            toast.error('Please fill in all required fields and select a plan');
            return;
        }

        setIsLoading(true);

        try {
            const response = await fetch(`http://localhost:5003/api/payments/${inventionId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    Payment_Package: selectedPlan,
                    Inventor_Email: inventorEmail,
                }),
            });

            const data = await response.json();

            if (response.ok && data.session_url) {
                // Redirect to Stripe Checkout
                window.open(data.session_url, '_blank');
                toast.success('Redirecting to payment...');
            } else {
                toast.error(data.error || 'Payment initialization failed');
            }
        } catch (error) {
            console.error('Payment error:', error);
            toast.error('Failed to initialize payment. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4">
            <div className="max-w-7xl mx-auto">
                <div className="text-center mb-12">
                    <h1 className="text-4xl font-bold text-gray-900 mb-4">
                        Choose Your Patent Package
                    </h1>
                    <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                        Select the perfect package for your invention needs. All packages include professional support and guidance.
                    </p>
                </div>

                {/* User Information Form */}
                <div className="max-w-md mx-auto mb-12">
                    <Card className="shadow-lg">
                        <CardHeader>
                            <CardTitle className="text-center">Your Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <Label htmlFor="inventionId">Invention ID</Label>
                                <Input
                                    id="inventionId"
                                    type="text"
                                    placeholder="Enter your invention ID"
                                    value={inventionId}
                                    onChange={(e) => setInventionId(e.target.value)}
                                    className="mt-1"
                                />
                            </div>
                            <div>
                                <Label htmlFor="email">Email Address</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    placeholder="Enter your email"
                                    value={inventorEmail}
                                    onChange={(e) => setInventorEmail(e.target.value)}
                                    className="mt-1"
                                />
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Pricing Cards */}
                <div className="grid md:grid-cols-3 gap-8 mb-12">
                    {pricingPlans.map((plan) => (
                        <Card
                            key={plan.name}
                            className={`relative cursor-pointer transition-all duration-300 hover:shadow-2xl ${
                                selectedPlan === plan.name
                                    ? 'ring-2 ring-blue-500 shadow-xl scale-105'
                                    : 'hover:scale-105'
                            } ${plan.popular ? 'border-2 border-blue-500' : ''}`}
                            onClick={() => setSelectedPlan(plan.name)}
                        >
                            {plan.popular && (
                                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                                </div>
                            )}

                            <CardHeader className="text-center pb-4">
                                <div className="flex justify-center mb-3">
                                    {plan.icon}
                                </div>
                                <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
                                <div className="mt-4">
                                    <span className="text-4xl font-bold text-gray-900">${plan.price}</span>
                                    <span className="text-gray-600 ml-2">one-time</span>
                                </div>
                            </CardHeader>

                            <CardContent>
                                <ul className="space-y-3">
                                    {plan.features.map((feature, index) => (
                                        <li key={index} className="flex items-start">
                                            <Check className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                                            <span className="text-gray-700">{feature}</span>
                                        </li>
                                    ))}
                                </ul>
                            </CardContent>

                            <CardFooter>
                                <Button
                                    className={`w-full ${
                                        selectedPlan === plan.name
                                            ? 'bg-blue-600 hover:bg-blue-700'
                                            : 'bg-gray-600 hover:bg-gray-700'
                                    }`}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        setSelectedPlan(plan.name);
                                    }}
                                >
                                    {selectedPlan === plan.name ? 'Selected' : 'Select Plan'}
                                </Button>
                            </CardFooter>
                        </Card>
                    ))}
                </div>

                {/* Proceed to Payment Button */}
                <div className="text-center">
                    <Button
                        size="lg"
                        onClick={handlePayment}
                        disabled={!selectedPlan || !inventionId || !inventorEmail || isLoading}
                        className="bg-blue-600 hover:bg-blue-700 px-8 py-3 text-lg font-semibold"
                    >
                        {isLoading ? 'Processing...' : 'Proceed to Payment'}
                    </Button>

                    {selectedPlan && (
                        <p className="mt-4 text-gray-600">
                            You selected: <span className="font-semibold">{selectedPlan}</span> -
                            ${pricingPlans.find(p => p.name === selectedPlan)?.price}
                        </p>
                    )}
                </div>

                {/* Security Notice */}
                <div className="max-w-2xl mx-auto mt-12 text-center">
                    <p className="text-sm text-gray-500">
                        🔒 Secure payment processing powered by Stripe. Your payment information is encrypted and secure.
                    </p>
                </div>
            </div>
        </div>
    );
};

export default Payment;


