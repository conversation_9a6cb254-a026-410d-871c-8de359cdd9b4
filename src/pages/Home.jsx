import React from 'react'
import { useNavigate } from 'react-router-dom'
import '../index.css'
import AppBar from '../components/Appbar.jsx'
import { Button } from '../components/payment/button'

const Home = () => {
    const navigate = useNavigate();

    const handleGoToPayment = () => {
        navigate('/payment');
    };

    return (
        <div className="absolute w-full h-full">
            <AppBar position="absolute" >
                <div>Header</div>
            </AppBar>
            <div className="flex flex-col items-center justify-center h-full space-y-6">
                <h1 className="text-4xl font-bold text-gray-900">Welcome to InnoVest</h1>
                <p className="text-xl text-gray-600 text-center max-w-2xl">
                    Protect your innovations with our comprehensive patent services.
                    Choose from our flexible packages designed to meet your specific needs.
                </p>
                <Button
                    onClick={handleGoToPayment}
                    className="bg-blue-600 hover:bg-blue-700 px-8 py-3 text-lg font-semibold"
                >
                    Get Started with Patent Protection
                </Button>
            </div>
        </div>
    )
}

export default Home
